import re
import requests
import concurrent.futures
import time
import threading
import os
import sys
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL证书警告
requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)

print_lock = threading.Lock()

# 检测是否支持颜色输出
USE_COLORS = (
    '--no-color' not in sys.argv and
    os.getenv('TERM') not in [None, 'dumb'] and
    hasattr(sys.stdout, 'isatty') and
    sys.stdout.isatty()
)

def colorize(text, color_code):
    """根据环境决定是否使用颜色"""
    if USE_COLORS:
        return f"\033[{color_code}m{text}\033[0m"
    return text


def load_mirrors_from_md(file_path):
    """
    从Markdown文件读取有效镜像列表
    格式要求：第一列为`包裹的镜像地址，第二列为状态（支持：正常/新增）
    """
    valid_status = {"正常", "新增"}
    pattern = r"`([\w\.\-]+)`.*?\|.*?(\S+)\s*\|"

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # 跳过表头前两行
            for _ in range(2): next(f)

            return [
                re.search(pattern, line).group(1)
                for line in f
                if re.search(pattern, line)
                   and re.search(pattern, line).group(2) in valid_status
            ]
    except Exception as e:
        print(f"{colorize('Error reading markdown file: ' + str(e), '31')}")
        return []


def test_mirror(mirror):
    url = f"https://{mirror}"
    success = 0
    total_time = 0.0
    attempts = 3
    log_buffer = []  # 日志缓存器

    # 初始化日志
    log_buffer.append(f"\n{colorize('▶ Testing ' + url, '1')}")

    for i in range(attempts):
        attempt_log = ""
        try:
            start_time = time.time()
            response = requests.head(
                f"{url}/v2/",
                timeout=5,
                verify=False
            )
            elapsed = time.time() - start_time

            if response.status_code in (200, 401):
                success += 1
                total_time += elapsed
                status_desc = colorize(f"{response.status_code} OK", "32")
            else:
                status_desc = colorize(f"{response.status_code} Error", "33")

            attempt_log = f"  Attempt {i + 1}: {status_desc} ({elapsed:.2f}s)"

        except Exception as e:
            elapsed = time.time() - start_time if 'start_time' in locals() else 0
            error_msg = str(e).split(":")[0]
            attempt_log = f"  Attempt {i + 1}: {colorize(f'Failed ({error_msg})', '31')}"

        finally:
            log_buffer.append(attempt_log)
            time.sleep(1)

    # 计算统计指标
    success_rate = success / attempts
    avg_time = total_time / success if success > 0 else float('inf')

    # 格式化平均响应时间显示
    avg_time_str = f"{avg_time:.2f}s" if avg_time != float('inf') else "N/A"

    # 添加结果摘要
    status_icon = colorize("✓", "32") if success_rate > 0.5 else colorize("✗", "31")
    log_buffer.append(
        f"{status_icon} 最终结果: "
        f"成功率 {success_rate * 100:.1f}% | "
        f"平均响应 {avg_time_str}"
    )

    # 原子化输出完整日志
    with print_lock:
        print("\n".join(log_buffer))

    return {
        "url": url,
        "success_rate": success_rate,
        "avg_time": avg_time
    }


if __name__ == "__main__":
    # 显示使用说明
    if '--help' in sys.argv or '-h' in sys.argv:
        print("Docker 镜像加速器测试工具")
        print("用法: python doceriotest.py [选项]")
        print("选项:")
        print("  --no-color    禁用颜色输出（适用于不支持ANSI颜色的终端）")
        print("  --help, -h    显示此帮助信息")
        print("\n注意: 如果在 macOS 终端中看到 \\033[31m 这样的字符，请使用 --no-color 选项")
        exit(0)

    # 从当前目录读取 mirrors.md 文件
    MIRROR_FILE = "mirrors.md"
    mirror_list = load_mirrors_from_md(MIRROR_FILE)

    if not mirror_list:
        print(colorize("未找到有效镜像地址，请检查markdown文件格式！", "31"))
        exit(1)

    print(colorize(f"成功加载 {len(mirror_list)} 个有效镜像", "1"))

    print(colorize("\n开始测试镜像加速器...", "1"))
    start_time = time.time()

    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = {executor.submit(test_mirror, mirror): mirror for mirror in mirror_list}

        for future in concurrent.futures.as_completed(futures):
            data = future.result()
            results.append(data)
            # 移除这里的重复输出，因为 test_mirror 函数已经输出了详细日志

    # 排序逻辑：成功率 > 响应时间
    sorted_results = sorted(results,
                            key=lambda x: (-x['success_rate'], x['avg_time']))

    print(f"\n{colorize('测试结果排序（最佳到最差）：', '1')}")
    for idx, res in enumerate(sorted_results, 1):
        if res["success_rate"] > 0.7:
            url_colored = colorize(res['url'], "32")
        elif res["success_rate"] > 0.3:
            url_colored = colorize(res['url'], "33")
        else:
            url_colored = colorize(res['url'], "31")

        # 格式化响应时间显示
        avg_time_str = f"{res['avg_time']:.2f}s" if res['avg_time'] != float('inf') else "N/A"
        print(f"{idx:2d}. {url_colored} "
              f"(成功率: {res['success_rate'] * 100:.1f}%, "
              f"响应: {avg_time_str})")

    # 原有排序输出保持不变...
    print(f"\n总测试时间: {time.time() - start_time:.1f}秒")

    # 新增有效镜像列表输出 ------------------------------------------
    print(f"\n{colorize('可用镜像列表（过滤零成功率镜像）：', '1')}")
    valid_mirrors = [
        res["url"]
        for res in sorted_results
        if res["success_rate"] > 0
    ]

    # 控制台彩色输出
    for idx, url in enumerate(valid_mirrors, 1):
        print(f"{idx:2d}. {colorize(url, '34')}")

    # 同时生成纯文本文件
    with open("valid_mirrors.txt", "w") as f:
        f.write("\n".join(valid_mirrors))

    print(f"\n{colorize(f'已生成 {len(valid_mirrors)} 个有效镜像到 valid_mirrors.txt', '36')}")
